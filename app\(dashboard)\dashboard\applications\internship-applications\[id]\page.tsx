"use client";

import React from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useGetMyInternshipApplicationsQuery } from '@/store/internshipApplication-feature/applicationApi';
import { useGetUserByIdQuery } from '@/store/features/userApi';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Calendar,
  User,
  Building2,
  MapPin,
  DollarSign,
  Clock,
  FileText,
  Mail,
  Phone,
  Briefcase,
  GraduationCap,
  Award,
  Globe,
  Users,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import { useAppSelector } from '@/store';

const InternshipApplicationDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const applicationId = params.id as string;
  const currentUser = useAppSelector(state => state.auth.user);

  // Since the API now expects internshipId, we need to get all applications first
  // and then find the specific application by applicationId
  const {
    data: applicationData,
    isLoading: applicationLoading,
    isError: applicationError
  } = useGetMyInternshipApplicationsQuery({ page: 1, limit: 100 });

  // Find the specific application by ID from the user's applications
  const application = applicationData?.data?.applications?.find(
    (app) => app._id === applicationId
  );

  console.log("internship application data", applicationData);
  

  // Get applicant details if current user is not the applicant
  const applicantId = typeof application?.applicant === 'object' 
    ? application.applicant._id 
    : application?.applicant;

  const shouldFetchApplicantDetails = applicantId && applicantId !== currentUser?._id;

  const {
    data: applicantData,
    isLoading: applicantLoading
  } = useGetUserByIdQuery(applicantId || '', {
    skip: !shouldFetchApplicantDetails
  });

  const applicantDetails = shouldFetchApplicantDetails ? applicantData?.data : currentUser;

  // Debug log to check the structure
  console.log('Applicant Details - for internship:', applicantDetails);

  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'bg-gray-100 text-gray-800',
      'applied': 'bg-blue-100 text-blue-800',
      'under-review': 'bg-yellow-100 text-yellow-800',
      'shortlisted': 'bg-purple-100 text-purple-800',
      'interview': 'bg-orange-100 text-orange-800',
      'offered': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800',
      'withdrawn': 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  const formatDateRange = (startDate: string, endDate?: string) => {
    try {
      const start = startDate ? format(new Date(startDate), 'MMM yyyy') : 'Unknown';
      const end = endDate ? format(new Date(endDate), 'MMM yyyy') : 'Present';
      return `${start} - ${end}`;
    } catch {
      return 'Date range not available';
    }
  };

  const getInternshipTitle = (internship: any) => {
    return typeof internship === 'object' ? internship.title : 'Internship Title Not Available';
  };

  const getCompanyName = (internship: any) => {
    if (typeof internship === 'object' && internship.company) {
      // If company is an object, get the name property
      if (typeof internship.company === 'object' && internship.company.name) {
        return internship.company.name;
      }
      // If company is a string, return it directly
      if (typeof internship.company === 'string') {
        return internship.company;
      }
    }
    return 'Company Not Available';
  };

  const getLocation = (internship: any) => {
    if (typeof internship === 'object' && internship.location) {
      // If location is an object, try to build a location string
      if (typeof internship.location === 'object') {
        const parts = [];
        if (internship.location.city) parts.push(internship.location.city);
        if (internship.location.state) parts.push(internship.location.state);
        if (internship.location.country) parts.push(internship.location.country);
        return parts.length > 0 ? parts.join(', ') : 'Location Available';
      }
      // If location is a string, return it directly
      if (typeof internship.location === 'string') {
        return internship.location;
      }
    }
    return 'Location Not Available';
  };

  const getUserRole = () => {
    if (!currentUser?._id || !application) return 'unknown';
    
    const applicantId = typeof application.applicant === 'object'
      ? application.applicant._id
      : application.applicant;

    return applicantId === currentUser._id ? 'applicant' : 'employer';
  };

  if (applicationLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading application details...</span>
        </div>
      </div>
    );
  }

  if (applicationError || (!applicationLoading && !application)) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Application Not Found</h1>
          <p className="text-gray-600 mb-4">The application you're looking for doesn't exist or you don't have permission to view it.</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Early return if application is not loaded yet
  if (!application) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading application details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Applications
        </Button>
        
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Internship Application Details
            </h1>
            <div className="flex items-center space-x-4">
              <Badge className={getStatusColor(application.status)}>
                {application.status.replace('-', ' ').toUpperCase()}
              </Badge>
              <Badge variant="outline">
                {getUserRole() === 'applicant' ? 'Your Application' : 'Received Application'}
              </Badge>
            </div>
          </div>
          <div className="text-right text-sm text-gray-600">
            <p>Applied on {formatDate(application.createdAt)}</p>
            {application.updatedAt !== application.createdAt && (
              <p>Updated on {formatDate(application.updatedAt)}</p>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {/* Internship Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="h-5 w-5 mr-2" />
              Internship Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {getInternshipTitle(application.Internship)}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <Building2 className="h-4 w-4 mr-1" />
                  {getCompanyName(application.Internship)}
                </span>
                <span className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {getLocation(application.Internship)}
                </span>
              </div>
            </div>

            {/* Internship Description */}
            {(application.Internship as any)?.description && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Internship Description</h4>
                <p className="text-gray-700 leading-relaxed">{(application.Internship as any).description}</p>
              </div>
            )}

            {/* Internship Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
              {(application.Internship as any)?.workingHours && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Working Hours</p>
                    <p className="font-medium">{(application.Internship as any).workingHours}</p>
                  </div>
                </div>
              )}
              {(application.Internship as any)?.workPlaceType && (
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Work Type</p>
                    <p className="font-medium capitalize">{(application.Internship as any).workPlaceType.replace('-', ' ')}</p>
                  </div>
                </div>
              )}
              {(application.Internship as any)?.mode && (
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Work Mode</p>
                    <p className="font-medium capitalize">{(application.Internship as any).mode}</p>
                  </div>
                </div>
              )}
              {(application.Internship as any)?.openings && (
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Openings</p>
                    <p className="font-medium">{(application.Internship as any).openings} position{(application.Internship as any).openings > 1 ? 's' : ''}</p>
                  </div>
                </div>
              )}
              {(application.Internship as any)?.applicationDeadline && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Application Deadline</p>
                    <p className="font-medium">{formatDate((application.Internship as any).applicationDeadline)}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Working Days */}
            {(application.Internship as any)?.workingDays && (application.Internship as any).workingDays.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Working Days</h4>
                <div className="flex flex-wrap gap-2">
                  {(application.Internship as any).workingDays.map((day: string, index: number) => (
                    <Badge key={index} variant="outline">{day}</Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Skills Required */}
            {(application.Internship as any)?.skills && (application.Internship as any).skills.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Skills Required</h4>
                <div className="space-y-2">
                  {(application.Internship as any).skills.map((skill: any, index: number) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="font-medium">{skill.skill}</span>
                      <Badge variant={skill.level === 'expert' ? 'default' : skill.level === 'advanced' ? 'secondary' : 'outline'}>
                        {skill.level}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Responsibilities */}
            {(application.Internship as any)?.responsibilities && (application.Internship as any).responsibilities.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Key Responsibilities</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {(application.Internship as any).responsibilities.map((responsibility: any, index: number) => (
                    <li key={index}>{responsibility.title}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Qualifications */}
            {(application.Internship as any)?.qualification && (application.Internship as any).qualification.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Qualifications</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {(application.Internship as any).qualification.map((qual: string, index: number) => (
                    <li key={index}>{qual}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Eligibility Criteria */}
            {(application.Internship as any)?.eligibilityCriteria && (application.Internship as any).eligibilityCriteria.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Eligibility Criteria</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {(application.Internship as any).eligibilityCriteria.map((criteria: string, index: number) => (
                    <li key={index}>{criteria}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Perks */}
            {(application.Internship as any)?.perks && (application.Internship as any).perks.length > 0 && (
              <div className="pt-4">
                <h4 className="font-semibold text-gray-900 mb-2">Perks & Benefits</h4>
                <div className="flex flex-wrap gap-2">
                  {(application.Internship as any).perks.map((perk: any, index: number) => (
                    <Badge key={index} variant="outline">{perk.title}</Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Company Information */}
        {typeof application.Internship === 'object' && (application.Internship as any).company && typeof (application.Internship as any).company === 'object' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="h-5 w-5 mr-2" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {(application.Internship as any).company.name}
                </h3>
                {(application.Internship as any).company.description && (
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {(application.Internship as any).company.description}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(application.Internship as any).company.industry && (
                  <div className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Industry</p>
                      <p className="font-medium">{(application.Internship as any).company.industry}</p>
                    </div>
                  </div>
                )}
                {(application.Internship as any).company.companySize && (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Company Size</p>
                      <p className="font-medium">{(application.Internship as any).company.companySize} employees</p>
                    </div>
                  </div>
                )}
                {(application.Internship as any).company.founded && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Founded</p>
                      <p className="font-medium">{formatDate((application.Internship as any).company.founded)}</p>
                    </div>
                  </div>
                )}
                {(application.Internship as any).company.country && (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Country</p>
                      <p className="font-medium">{(application.Internship as any).company.country}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Company Categories */}
              {(application.Internship as any).company.category && (application.Internship as any).company.category.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Categories</h4>
                  <div className="flex flex-wrap gap-2">
                    {(application.Internship as any).company.category.map((cat: string, index: number) => (
                      <Badge key={index} variant="outline">{cat}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Information */}
              {((application.Internship as any).company.emails && (application.Internship as any).company.emails.length > 0) ||
               ((application.Internship as any).company.phones && (application.Internship as any).company.phones.length > 0) && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                  <div className="space-y-2">
                    {(application.Internship as any).company.emails && (application.Internship as any).company.emails.map((emailObj: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">{emailObj.label}</p>
                          <p className="font-medium">{emailObj.email}</p>
                        </div>
                      </div>
                    ))}
                    {(application.Internship as any).company.phones && (application.Internship as any).company.phones.map((phoneObj: any, index: number) => (
                      <div key={index} className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">{phoneObj.label}</p>
                          <p className="font-medium">{phoneObj.phone}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Selection Process */}
        {typeof application.Internship === 'object' && (application.Internship as any).selectionProcess && (application.Internship as any).selectionProcess.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Selection Process
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(application.Internship as any).selectionProcess.map((stage: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {stage.sortOrder || index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{stage.stage}</h4>
                      <p className="text-gray-600 text-sm mt-1">{stage.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Interview Rounds */}
        {typeof application.Internship === 'object' && (application.Internship as any).interviews && (application.Internship as any).interviews.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Interview Rounds
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(application.Internship as any).interviews.map((interview: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {interview.sortOrder || index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{interview.roundTitle}</h4>
                      <p className="text-gray-600 text-sm mt-1">{interview.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Cover Letter */}
        {application.coverLetter && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Cover Letter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {application.coverLetter}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Questions and Answers */}
        {application.answers && application.answers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Application Questions & Answers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {application.answers.map((answer: any, index: number) => {
                  // Find the corresponding question from internship data
                  const question = typeof application.Internship === 'object' && (application.Internship as any).questions
                    ? (application.Internship as any).questions.find((q: any) => q.questionId === answer.questionId)
                    : null;

                  return (
                    <div key={index}>
                      <div className="mb-2">
                        <p className="font-medium text-gray-900">
                          {question ? question.question : `Question ${answer.questionId}`}
                        </p>
                        {question && question.hint && (
                          <p className="text-sm text-gray-500 mt-1">{question.hint}</p>
                        )}
                        {question && question.isRequired && (
                          <Badge variant="outline" className="mt-1">Required</Badge>
                        )}
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-gray-700">
                          {Array.isArray(answer.answer)
                            ? answer.answer.join(', ')
                            : typeof answer.answer === 'string'
                            ? answer.answer
                            : typeof answer.answer === 'object'
                            ? JSON.stringify(answer.answer)
                            : String(answer.answer)
                          }
                        </p>
                      </div>
                      {index < application.answers.length - 1 && <Separator className="mt-6" />}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Applicant Details */}
        {applicantDetails && typeof applicantDetails === 'object' && applicantDetails.firstName && applicantDetails.email && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                Applicant Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {applicantLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2">Loading applicant details...</span>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Personal Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">Full Name</p>
                          <p className="font-medium">
                            {typeof applicantDetails.firstName === 'string' ? applicantDetails.firstName : ''} {typeof applicantDetails.lastName === 'string' ? applicantDetails.lastName : ''}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">Email</p>
                          <p className="font-medium">
                            {typeof applicantDetails.email === 'string' ? applicantDetails.email : 'Email not available'}
                          </p>
                        </div>
                      </div>
                      {applicantDetails.phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Phone</p>
                            <p className="font-medium">
                              {typeof applicantDetails.phone === 'string'
                                ? applicantDetails.phone
                                : 'Phone information available'
                              }
                            </p>
                          </div>
                        </div>
                      )}
                      {(applicantDetails as any).fatherName && (
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Father's Name</p>
                            <p className="font-medium">{(applicantDetails as any).fatherName}</p>
                          </div>
                        </div>
                      )}
                      {(applicantDetails as any).motherName && (
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600">Mother's Name</p>
                            <p className="font-medium">{(applicantDetails as any).motherName}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional Details */}
                  {(applicantDetails.dob || applicantDetails.gender) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Additional Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {applicantDetails.dob && (
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Date of Birth</p>
                                <p className="font-medium">
                                  {applicantDetails.dob ? format(new Date(applicantDetails.dob), 'MMM dd, yyyy') : 'Date not available'}
                                </p>
                              </div>
                            </div>
                          )}
                          {applicantDetails.gender && (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600">Gender</p>
                                <p className="font-medium">
                                  {typeof applicantDetails.gender === 'string'
                                    ? applicantDetails.gender
                                    : 'Gender information available'
                                  }
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Professional Information */}
                  {(
                    ((applicantDetails as any).skills && (applicantDetails as any).skills.length > 0) ||
                    ((applicantDetails as any).qualifications && (applicantDetails as any).qualifications.length > 0) ||
                    ((applicantDetails as any).experiences && (applicantDetails as any).experiences.length > 0) ||
                    ((applicantDetails as any).certificates && (applicantDetails as any).certificates.length > 0)
                  ) && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Professional Information</h4>

                        {/* Skills */}
                        {(applicantDetails as any).skills && (applicantDetails as any).skills.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Skills & Expertise</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {(applicantDetails as any).skills.map((skill: any, index: number) => (
                                <div key={skill._id || index} className="bg-gray-50 p-3 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <h6 className="font-medium text-gray-900 capitalize">{skill.name}</h6>
                                    <Badge variant={
                                      skill.level === 'expert' ? 'default' :
                                      skill.level === 'advanced' ? 'secondary' : 'outline'
                                    }>
                                      {skill.level}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600">
                                    <Star className="h-3 w-3 mr-1" />
                                    <span>{skill.monthsOfExperience} months experience</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Qualifications */}
                        {(applicantDetails as any).qualifications && (applicantDetails as any).qualifications.length > 0 && (
                          <div className="mb-4">
                            <h5 className="font-medium text-gray-800 mb-2">Qualifications</h5>
                            <div className="flex flex-wrap gap-2">
                              {(applicantDetails as any).qualifications.map((qualId: string, index: number) => (
                                <Badge key={index} variant="outline">
                                  <GraduationCap className="h-3 w-3 mr-1" />
                                  Qualification ID: {qualId}
                                </Badge>
                              ))}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              Total Qualifications: {(applicantDetails as any).qualifications.length}
                            </p>
                          </div>
                        )}

                        {/* Work Experience */}
                        {(applicantDetails as any).experiences && (applicantDetails as any).experiences.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Work Experience</h5>
                            <div className="space-y-4">
                              {(applicantDetails as any).experiences.map((exp: any, index: number) => (
                                <div key={exp._id || index} className="bg-gray-50 p-4 rounded-lg">
                                  <div className="flex items-start justify-between mb-2">
                                    <div>
                                      <h6 className="font-semibold text-gray-900">{exp.designation}</h6>
                                      <p className="text-blue-600 font-medium">{exp.company}</p>
                                    </div>
                                    <Badge variant="outline" className="capitalize">
                                      {exp.employmentType}
                                    </Badge>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm text-gray-600">
                                    <div className="flex items-center">
                                      <Calendar className="h-3 w-3 mr-1" />
                                      <span>{formatDateRange(exp.startDate, exp.endDate)}</span>
                                    </div>
                                    <div className="flex items-center">
                                      <MapPin className="h-3 w-3 mr-1" />
                                      <span>{exp.location} ({exp.locationType})</span>
                                    </div>
                                    {exp.inHandSalary && (
                                      <div className="flex items-center">
                                        <DollarSign className="h-3 w-3 mr-1" />
                                        <span>₹{exp.inHandSalary.toLocaleString()}/month</span>
                                      </div>
                                    )}
                                  </div>
                                  {exp.description && (
                                    <p className="text-gray-700 text-sm">{exp.description}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Certificates */}
                        {(applicantDetails as any).certificates && (applicantDetails as any).certificates.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Certificates & Achievements</h5>
                            <div className="space-y-3">
                              {(applicantDetails as any).certificates.map((cert: any, index: number) => (
                                <div key={cert._id || index} className="bg-gray-50 p-4 rounded-lg">
                                  <div className="flex items-start">
                                    <Award className="h-5 w-5 mr-3 text-blue-600 mt-1" />
                                    <div className="flex-1">
                                      <div className="flex items-start justify-between mb-2">
                                        <div>
                                          <h6 className="font-semibold text-gray-900">{cert.certificateName}</h6>
                                          <p className="text-blue-600 font-medium">{cert.instituteName}</p>
                                        </div>
                                        <Badge variant="outline" className="capitalize">
                                          {cert.certificateType}
                                        </Badge>
                                      </div>

                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm text-gray-600">
                                        <div className="flex items-center">
                                          <Calendar className="h-3 w-3 mr-1" />
                                          <span>Issued: {formatDate(cert.issueDate)}</span>
                                        </div>
                                        {cert.expiryDate && (
                                          <div className="flex items-center">
                                            <Calendar className="h-3 w-3 mr-1" />
                                            <span>Expires: {formatDate(cert.expiryDate)}</span>
                                          </div>
                                        )}
                                      </div>

                                      {cert.description && (
                                        <p className="text-gray-700 text-sm mb-3">{cert.description}</p>
                                      )}

                                      {cert.verificationUrl && (
                                        <div className="flex items-center">
                                          <Globe className="h-3 w-3 mr-1 text-green-600" />
                                          <a
                                            href={cert.verificationUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-green-600 hover:text-green-800 text-sm"
                                          >
                                            Verify Certificate
                                          </a>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Important Links */}
                        {(applicantDetails as any).importantLinks && (applicantDetails as any).importantLinks.length > 0 && (
                          <div className="mb-6">
                            <h5 className="font-medium text-gray-800 mb-3">Important Links</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {(applicantDetails as any).importantLinks.map((link: any, index: number) => (
                                <div key={link._id || index} className="bg-gray-50 p-3 rounded-lg">
                                  <div className="flex items-center">
                                    <Globe className="h-4 w-4 mr-2 text-gray-500" />
                                    <div className="flex-1">
                                      <p className="font-medium text-gray-900">{link.title}</p>
                                      <a
                                        href={link.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 text-sm break-all"
                                      >
                                        {link.url}
                                      </a>
                                      <div className="flex items-center justify-between mt-2">
                                        <Badge variant="outline" className="text-xs">
                                          {link.type}
                                        </Badge>
                                        {link.isVerified ? (
                                          <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                            Verified
                                          </Badge>
                                        ) : (
                                          <Badge variant="outline" className="text-xs">
                                            Not Verified
                                          </Badge>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </>
                  )}

                  {/* Emergency Contacts */}
                  {(applicantDetails as any).emergencyContacts && (applicantDetails as any).emergencyContacts.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Emergency Contacts</h4>
                        <div className="space-y-3">
                          {(applicantDetails as any).emergencyContacts.map((contact: any, index: number) => (
                            <div key={index} className="bg-gray-50 p-3 rounded-lg">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                <div>
                                  <p className="text-sm text-gray-600">Name</p>
                                  <p className="font-medium">{contact.name || 'Name not provided'}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600">Relationship</p>
                                  <p className="font-medium">{contact.relationship || 'Not specified'}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600">Phone</p>
                                  <p className="font-medium">{contact.phone || 'Phone not provided'}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600">Email</p>
                                  <p className="font-medium">{contact.email || 'Email not provided'}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Identity Documents */}
                  {(applicantDetails as any).identityDocs && (applicantDetails as any).identityDocs.length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Identity Documents</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {(applicantDetails as any).identityDocs.map((docId: string, index: number) => (
                            <div key={index} className="bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2 text-gray-500" />
                                <div>
                                  <p className="text-sm text-gray-600">Document ID</p>
                                  <p className="font-mono text-xs">{docId}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* User Type Information */}
                  {applicantDetails.userType && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Account Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <div>
                              <p className="text-sm text-gray-600">User Type</p>
                              <p className="font-medium capitalize">
                                {typeof applicantDetails.userType === 'string'
                                  ? applicantDetails.userType
                                  : 'User type information available'
                                }
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <div>
                              <p className="text-sm text-gray-600">Applicant ID</p>
                              <p className="font-mono text-xs bg-gray-100 p-1 rounded">
                                {(applicantDetails as any)._id || 'ID not available'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Application Status & Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Application Status & Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Current Status</p>
                  <Badge className={getStatusColor(application.status)}>
                    {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                  </Badge>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Last Updated</p>
                  <p className="font-medium">{formatDate(application.updatedAt)}</p>
                </div>
              </div>

              {/* Review Logs */}
              {application.reviewLogs && application.reviewLogs.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Review History</h4>
                  <div className="space-y-3">
                    {application.reviewLogs.map((log: any, index: number) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline">{log.status || 'Status Update'}</Badge>
                          <span className="text-sm text-gray-500">
                            {log.date ? formatDate(log.date) : 'Date not available'}
                          </span>
                        </div>
                        {log.comment && (
                          <p className="text-gray-700 text-sm">{log.comment}</p>
                        )}
                        {log.reviewer && (
                          <p className="text-gray-600 text-xs mt-1">Reviewed by: {log.reviewer}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Interviews */}
              {(application as any).interviews && (application as any).interviews.length > 0 && (
                <div className="pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">Scheduled Interviews</h4>
                  <div className="space-y-3">
                    {(application as any).interviews.map((interview: any, index: number) => (
                      <div key={index} className="bg-blue-50 p-3 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-gray-900">
                            {interview.title || `Interview ${index + 1}`}
                          </h5>
                          {interview.status && (
                            <Badge variant={interview.status === 'completed' ? 'default' : 'outline'}>
                              {interview.status}
                            </Badge>
                          )}
                        </div>
                        {interview.date && (
                          <p className="text-sm text-gray-600">
                            <Calendar className="h-4 w-4 inline mr-1" />
                            {formatDate(interview.date)}
                          </p>
                        )}
                        {interview.time && (
                          <p className="text-sm text-gray-600">
                            <Clock className="h-4 w-4 inline mr-1" />
                            {interview.time}
                          </p>
                        )}
                        {interview.location && (
                          <p className="text-sm text-gray-600">
                            <MapPin className="h-4 w-4 inline mr-1" />
                            {interview.location}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Application Metadata */}
              <div className="pt-4 border-t">
                <h4 className="font-semibold text-gray-900 mb-3">Application Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Application ID</p>
                    <p className="font-mono text-xs bg-gray-100 p-1 rounded">{application._id}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Internship ID</p>
                    <p className="font-mono text-xs bg-gray-100 p-1 rounded">
                      {typeof application.Internship === 'object' ? (application.Internship as any)._id : application.Internship}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">Submitted On</p>
                    <p className="font-medium">{formatDate(application.createdAt)}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Is Deleted</p>
                    <p className="font-medium">{application.isDeleted ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InternshipApplicationDetailsPage;
